import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { BikeIcon as MotorbikeIcon, TrendingUp, Users } from "lucide-react"
import { HowItWorks } from "@/components/how-it-works"
import { RideSearch } from "@/components/ride-search"

export default function Home() {
  return (
    <div className="container mx-auto px-4 py-8">
      <section className="flex flex-col items-center justify-center py-12 text-center">
        <h1 className="text-4xl font-bold tracking-tight sm:text-5xl">Share Rides, Save Money</h1>
        <p className="mt-4 text-xl text-muted-foreground max-w-2xl">
          Connect with local two-wheeler owners for affordable daily commutes at just ₹5 per km.
        </p>
        <div className="mt-8 flex flex-col sm:flex-row gap-4">
          <Button asChild size="lg">
            <Link href="/find-ride">Find a Ride</Link>
          </Button>
          <Button asChild variant="outline" size="lg">
            <Link href="/offer-ride">Offer Your Vehicle</Link>
          </Button>
        </div>
      </section>

      <RideSearch />

      <section className="py-12">
        <div className="text-center mb-10">
          <h2 className="text-3xl font-bold">Why Choose Our Platform?</h2>
          <p className="mt-4 text-muted-foreground max-w-2xl mx-auto">
            Our two-wheeler sharing platform benefits both riders and vehicle owners
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center gap-4">
              <MotorbikeIcon className="h-8 w-8 text-primary" />
              <div>
                <CardTitle>Affordable Rides</CardTitle>
                <CardDescription>Just ₹5 per kilometer</CardDescription>
              </div>
            </CardHeader>
            <CardContent>
              <p>Save money on your daily commute with our affordable ride-sharing options.</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center gap-4">
              <TrendingUp className="h-8 w-8 text-primary" />
              <div>
                <CardTitle>Earn as an Owner</CardTitle>
                <CardDescription>₹3 per km goes to you</CardDescription>
              </div>
            </CardHeader>
            <CardContent>
              <p>Vehicle owners earn ₹3 per km. A daily 20km ride can earn you ₹60 per day!</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center gap-4">
              <Users className="h-8 w-8 text-primary" />
              <div>
                <CardTitle>Daily Route Matching</CardTitle>
                <CardDescription>Find regular commuters</CardDescription>
              </div>
            </CardHeader>
            <CardContent>
              <p>Our smart matching system connects you with riders who share your daily route.</p>
            </CardContent>
          </Card>
        </div>
      </section>

      <HowItWorks />

      <section className="py-12 bg-muted rounded-lg p-8 mt-12">
        <div className="text-center">
          <h2 className="text-3xl font-bold">Ready to Get Started?</h2>
          <p className="mt-4 text-muted-foreground max-w-2xl mx-auto">
            Join thousands of users who are already saving money and reducing their carbon footprint.
          </p>
          <div className="mt-8 flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild size="lg">
              <Link href="/register">Sign Up Now</Link>
            </Button>
            <Button asChild variant="outline" size="lg">
              <Link href="/learn-more">Learn More</Link>
            </Button>
          </div>
        </div>
      </section>
    </div>
  )
}

