#!/usr/bin/env node

/**
 * AI-Powered Development CLI Tool
 * Command-line interface for AI development assistant features
 */

const { program } = require('commander');
const fs = require('fs').promises;
const path = require('path');
const axios = require('axios');
const chalk = require('chalk');

class AIDevCLI {
  constructor() {
    this.mcpServerUrl = process.env.MCP_SERVER_URL || 'http://localhost:8080';
    this.setupCommands();
  }

  setupCommands() {
    program
      .name('ai-dev')
      .description('AI-Powered Development Assistant CLI')
      .version('1.0.0');

    // Analyze command
    program
      .command('analyze <file>')
      .description('Analyze code file for quality, issues, and suggestions')
      .option('-o, --output <format>', 'Output format (json|table)', 'table')
      .action(async (file, options) => {
        await this.analyzeFile(file, options);
      });

    // Generate tests command
    program
      .command('test <file>')
      .description('Generate comprehensive tests for a component')
      .option('-w, --write', 'Write test file to disk', false)
      .option('-o, --output <path>', 'Output path for test file')
      .action(async (file, options) => {
        await this.generateTests(file, options);
      });

    // Refactor command
    program
      .command('refactor <file>')
      .description('Get refactoring suggestions for code')
      .option('-a, --apply', 'Apply suggested refactoring (interactive)', false)
      .action(async (file, options) => {
        await this.suggestRefactoring(file, options);
      });

    // PR description command
    program
      .command('pr [branch]')
      .description('Generate PR description from git diff')
      .option('-b, --base <branch>', 'Base branch for comparison', 'main')
      .action(async (branch, options) => {
        await this.generatePRDescription(branch, options);
      });

    // Batch analyze command
    program
      .command('batch <pattern>')
      .description('Batch analyze files matching pattern')
      .option('-r, --recursive', 'Search recursively', false)
      .option('-e, --exclude <patterns>', 'Exclude patterns (comma-separated)')
      .action(async (pattern, options) => {
        await this.batchAnalyze(pattern, options);
      });

    // Health check command
    program
      .command('health')
      .description('Check AI services health')
      .action(async () => {
        await this.healthCheck();
      });

    // Setup command
    program
      .command('setup')
      .description('Setup AI development assistant')
      .action(async () => {
        await this.setup();
      });
  }

  async analyzeFile(filePath, options) {
    try {
      console.log(chalk.blue(`🔍 Analyzing: ${filePath}`));
      
      const codeContent = await fs.readFile(filePath, 'utf8');
      
      const response = await axios.post(`${this.mcpServerUrl}/ai-dev-assistant/analyze`, {
        filePath,
        codeContent
      });

      if (response.data.success) {
        const analysis = response.data.analysis.analysis;
        
        if (options.output === 'json') {
          console.log(JSON.stringify(analysis, null, 2));
        } else {
          this.displayAnalysisTable(analysis, filePath);
        }
      } else {
        console.error(chalk.red('❌ Analysis failed'));
      }

    } catch (error) {
      console.error(chalk.red(`❌ Error analyzing file: ${error.message}`));
      process.exit(1);
    }
  }

  async generateTests(filePath, options) {
    try {
      console.log(chalk.blue(`🧪 Generating tests for: ${filePath}`));
      
      const codeContent = await fs.readFile(filePath, 'utf8');
      
      const response = await axios.post(`${this.mcpServerUrl}/ai-dev-assistant/generate-tests`, {
        filePath,
        codeContent
      });

      if (response.data.success) {
        const testResult = response.data.testResult;
        
        console.log(chalk.green(`✅ Tests generated successfully`));
        console.log(chalk.cyan(`📊 Estimated coverage: ${testResult.coverage.estimated_coverage}%`));
        
        if (options.write) {
          const outputPath = options.output || testResult.testFilePath;
          await fs.writeFile(outputPath, testResult.testCode, 'utf8');
          console.log(chalk.green(`📝 Test file written to: ${outputPath}`));
        } else {
          console.log('\n' + chalk.yellow('Generated test code:'));
          console.log(testResult.testCode);
        }
      } else {
        console.error(chalk.red('❌ Test generation failed'));
      }

    } catch (error) {
      console.error(chalk.red(`❌ Error generating tests: ${error.message}`));
      process.exit(1);
    }
  }

  async suggestRefactoring(filePath, options) {
    try {
      console.log(chalk.blue(`🔧 Analyzing refactoring opportunities: ${filePath}`));
      
      const codeContent = await fs.readFile(filePath, 'utf8');
      
      const response = await axios.post(`${this.mcpServerUrl}/ai-dev-assistant/suggest-refactoring`, {
        filePath,
        codeContent
      });

      if (response.data.success) {
        const suggestions = response.data.suggestions.suggestions;
        
        console.log(chalk.green(`💡 Found ${suggestions.length} refactoring opportunities:`));
        
        suggestions.forEach((suggestion, index) => {
          console.log(chalk.cyan(`\n${index + 1}. ${suggestion.description}`));
          if (suggestion.effort) {
            console.log(chalk.gray(`   Effort: ${suggestion.effort}`));
          }
        });

        if (options.apply) {
          console.log(chalk.yellow('\n🚧 Interactive refactoring not yet implemented'));
        }
      } else {
        console.error(chalk.red('❌ Refactoring analysis failed'));
      }

    } catch (error) {
      console.error(chalk.red(`❌ Error analyzing refactoring: ${error.message}`));
      process.exit(1);
    }
  }

  async generatePRDescription(branch, options) {
    try {
      const currentBranch = branch || this.getCurrentBranch();
      console.log(chalk.blue(`📝 Generating PR description for: ${currentBranch}`));
      
      const gitDiff = this.getGitDiff(options.base, currentBranch);
      
      const response = await axios.post(`${this.mcpServerUrl}/ai-dev-assistant/generate-pr`, {
        gitDiff,
        branchName: currentBranch
      });

      if (response.data.success) {
        const prDescription = response.data.prDescription;
        
        console.log(chalk.green('\n✅ PR Description Generated:'));
        console.log(chalk.cyan(`\n📋 Title: ${prDescription.title}`));
        console.log(chalk.white('\n📝 Description:'));
        console.log(prDescription.description);
      } else {
        console.error(chalk.red('❌ PR description generation failed'));
      }

    } catch (error) {
      console.error(chalk.red(`❌ Error generating PR description: ${error.message}`));
      process.exit(1);
    }
  }

  async batchAnalyze(pattern, options) {
    try {
      console.log(chalk.blue(`🔍 Batch analyzing files matching: ${pattern}`));
      
      const files = await this.findFiles(pattern, options);
      console.log(chalk.cyan(`Found ${files.length} files to analyze`));
      
      const fileContents = [];
      for (const file of files) {
        const content = await fs.readFile(file, 'utf8');
        fileContents.push({ filePath: file, codeContent: content });
      }
      
      const response = await axios.post(`${this.mcpServerUrl}/ai-dev-assistant/batch-analyze`, {
        files: fileContents
      });

      if (response.data.success) {
        const results = response.data.results;
        const summary = response.data.summary;
        
        console.log(chalk.green(`\n✅ Batch analysis complete:`));
        console.log(chalk.cyan(`📊 Total: ${summary.total}, Successful: ${summary.successful}, Failed: ${summary.failed}`));
        
        results.forEach(result => {
          if (result.success) {
            console.log(chalk.green(`✅ ${result.filePath}: Quality ${result.analysis.quality_score}/10`));
          } else {
            console.log(chalk.red(`❌ ${result.filePath}: ${result.error}`));
          }
        });
      } else {
        console.error(chalk.red('❌ Batch analysis failed'));
      }

    } catch (error) {
      console.error(chalk.red(`❌ Error in batch analysis: ${error.message}`));
      process.exit(1);
    }
  }

  async healthCheck() {
    try {
      console.log(chalk.blue('🔍 Checking AI services health...'));
      
      const response = await axios.get(`${this.mcpServerUrl}/ai-dev-assistant/health`);
      
      if (response.data.success) {
        const health = response.data.health;
        const services = response.data.services;
        
        console.log(chalk.green('✅ AI Development Assistant is healthy'));
        console.log(chalk.cyan(`📊 Ollama status: ${health.status}`));
        console.log(chalk.cyan(`🤖 Code Analyzer: ${services.codeAnalyzer}`));
        console.log(chalk.cyan(`🧪 Test Generator: ${services.testGenerator}`));
        
        if (health.models) {
          console.log(chalk.cyan(`📚 Available models: ${health.models}`));
        }
      } else {
        console.error(chalk.red('❌ AI services are not healthy'));
      }

    } catch (error) {
      console.error(chalk.red(`❌ Health check failed: ${error.message}`));
      console.log(chalk.yellow('💡 Make sure MCP server is running: npm run dev (in mcp-server directory)'));
      process.exit(1);
    }
  }

  async setup() {
    console.log(chalk.blue('🚀 Setting up AI Development Assistant...'));
    
    // Check prerequisites
    console.log(chalk.cyan('📋 Checking prerequisites...'));
    
    try {
      // Check if Ollama is installed
      const { execSync } = require('child_process');
      execSync('ollama --version', { stdio: 'ignore' });
      console.log(chalk.green('✅ Ollama is installed'));
    } catch (error) {
      console.log(chalk.yellow('⚠️  Ollama not found. Please install from https://ollama.ai'));
    }
    
    // Check MCP server
    try {
      await this.healthCheck();
    } catch (error) {
      console.log(chalk.yellow('⚠️  MCP server not running. Start with: npm run dev'));
    }
    
    console.log(chalk.green('\n✅ Setup complete! Use "ai-dev --help" to see available commands.'));
  }

  displayAnalysisTable(analysis, filePath) {
    console.log(chalk.green(`\n✅ Analysis Results for ${filePath}:`));
    console.log(chalk.cyan(`📊 Quality Score: ${analysis.quality_score}/10`));
    
    if (analysis.issues && analysis.issues.length > 0) {
      console.log(chalk.yellow(`\n⚠️  Issues Found (${analysis.issues.length}):`));
      analysis.issues.forEach((issue, index) => {
        const severity = issue.severity === 'high' ? chalk.red : 
                        issue.severity === 'medium' ? chalk.yellow : chalk.gray;
        console.log(severity(`  ${index + 1}. ${issue.description} (Line ${issue.line})`));
      });
    }
    
    if (analysis.optimizations && analysis.optimizations.length > 0) {
      console.log(chalk.blue(`\n🚀 Optimization Suggestions (${analysis.optimizations.length}):`));
      analysis.optimizations.forEach((opt, index) => {
        console.log(chalk.cyan(`  ${index + 1}. ${opt.description}`));
      });
    }
  }

  getCurrentBranch() {
    const { execSync } = require('child_process');
    return execSync('git branch --show-current', { encoding: 'utf8' }).trim();
  }

  getGitDiff(base, branch) {
    const { execSync } = require('child_process');
    return execSync(`git diff ${base}...${branch}`, { encoding: 'utf8' });
  }

  async findFiles(pattern, options) {
    const glob = require('glob');
    return new Promise((resolve, reject) => {
      glob(pattern, { 
        ignore: options.exclude ? options.exclude.split(',') : [],
        recursive: options.recursive 
      }, (err, files) => {
        if (err) reject(err);
        else resolve(files);
      });
    });
  }

  run() {
    program.parse();
  }
}

// Run CLI if called directly
if (require.main === module) {
  const cli = new AIDevCLI();
  cli.run();
}

module.exports = AIDevCLI;
