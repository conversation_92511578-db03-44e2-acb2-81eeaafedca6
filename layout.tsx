import type React from "react"
import type { Metada<PERSON> } from "next"
import "./globals.css"
import Link from "next/link"

export const metadata: Metadata = {
  title: "Two-Wheeler Sharing App",
  description: "Share rides, save money with our two-wheeler sharing platform",
    generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en">
      <body>
        <nav className="flex items-center justify-between p-4 border-b">
          <div>
            <Link href="/" className="text-2xl font-bold">
              RideShare
            </Link>
          </div>
          <div className="flex items-center space-x-4">
            <Link href="/" className="text-lg font-medium transition-colors hover:text-primary">
              Home
            </Link>
            <Link href="/find-ride" className="text-lg font-medium transition-colors hover:text-primary">
              Find Ride
            </Link>
            <Link href="/offer-ride" className="text-lg font-medium transition-colors hover:text-primary">
              Offer Ride
            </Link>
            <Link href="/payments" className="text-lg font-medium transition-colors hover:text-primary">
              Payments
            </Link>
            <Link href="/dashboard" className="text-lg font-medium transition-colors hover:text-primary">
              Dashboard
            </Link>
          </div>
        </nav>
        {children}
      </body>
    </html>
  )
}



import './globals.css'