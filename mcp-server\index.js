const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const mongoose = require('mongoose');
const { Server } = require('socket.io');
const http = require('http');
const redis = require('redis');
const winston = require('winston');
require('dotenv').config();

const toolsRouter = require('./routes/tools');
const notificationsRouter = require('./routes/notifications');
const rewardsRouter = require('./routes/rewards');
const aiRouter = require('./routes/ai');

// Initialize Express app
const app = express();
const server = http.createServer(app);
const io = new Server(server, {
  cors: {
    origin: process.env.FRONTEND_URL || "http://localhost:3000",
    methods: ["GET", "POST"]
  }
});

// Logger setup
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
    new winston.transports.File({ filename: 'logs/combined.log' })
  ]
});

// Redis client
const redisClient = redis.createClient({
  url: process.env.REDIS_URL || 'redis://redis:6379'
});

// Middleware
app.use(helmet());
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Request logging
app.use((req, res, next) => {
  logger.info(`${req.method} ${req.path}`, {
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });
  next();
});

// MongoDB connection
mongoose.connect(process.env.MONGODB_URI || 'mongodb://mongodb:27017/two-wheeler-sharing', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
})
.then(() => logger.info('Connected to MongoDB'))
.catch(err => logger.error('MongoDB connection error:', err));

// Redis connection
redisClient.connect()
  .then(() => logger.info('Connected to Redis'))
  .catch(err => logger.error('Redis connection error:', err));

// Socket.io connection handling
io.on('connection', (socket) => {
  logger.info('Client connected:', socket.id);
  
  socket.on('join_driver', (driverId) => {
    socket.join(`driver_${driverId}`);
    logger.info(`Driver ${driverId} joined room`);
  });
  
  socket.on('join_user', (userId) => {
    socket.join(`user_${userId}`);
    logger.info(`User ${userId} joined room`);
  });
  
  socket.on('disconnect', () => {
    logger.info('Client disconnected:', socket.id);
  });
});

// Make io available to routes
app.set('io', io);
app.set('redis', redisClient);
app.set('logger', logger);

// Routes
app.use('/tools', toolsRouter);
app.use('/notifications', notificationsRouter);
app.use('/rewards', rewardsRouter);
app.use('/ai', aiRouter);

// Health check
app.get('/health', (req, res) => {
  res.json({ 
    status: 'healthy', 
    timestamp: new Date().toISOString(),
    services: {
      mongodb: mongoose.connection.readyState === 1 ? 'connected' : 'disconnected',
      redis: redisClient.isReady ? 'connected' : 'disconnected'
    }
  });
});

// MCP Protocol endpoints
app.get('/mcp/tools', (req, res) => {
  res.json({
    tools: [
      {
        name: 'notify_driver',
        description: 'Send notification to a specific driver',
        parameters: {
          type: 'object',
          properties: {
            driverId: { type: 'string' },
            message: { type: 'string' },
            taskId: { type: 'string' },
            priority: { type: 'string', enum: ['low', 'medium', 'high'] }
          },
          required: ['driverId', 'message']
        }
      },
      {
        name: 'assign_task',
        description: 'Assign a ride task to a driver',
        parameters: {
          type: 'object',
          properties: {
            taskId: { type: 'string' },
            driverId: { type: 'string' },
            userId: { type: 'string' },
            route: { type: 'object' },
            estimatedEarnings: { type: 'number' }
          },
          required: ['taskId', 'driverId', 'userId']
        }
      },
      {
        name: 'update_rewards',
        description: 'Update user or driver reward points',
        parameters: {
          type: 'object',
          properties: {
            userId: { type: 'string' },
            points: { type: 'number' },
            reason: { type: 'string' },
            multiplier: { type: 'number', default: 1 }
          },
          required: ['userId', 'points', 'reason']
        }
      },
      {
        name: 'calculate_pricing',
        description: 'Calculate dynamic pricing for a ride',
        parameters: {
          type: 'object',
          properties: {
            distance: { type: 'number' },
            duration: { type: 'number' },
            demand: { type: 'string', enum: ['low', 'medium', 'high'] },
            weather: { type: 'string' },
            timeOfDay: { type: 'string' }
          },
          required: ['distance']
        }
      },
      {
        name: 'predict_demand_hotspots',
        description: 'Predict demand hotspots using AI analysis',
        parameters: {
          type: 'object',
          properties: {
            time_horizon: { type: 'number', default: 60 },
            location_filter: { type: 'string' }
          },
          required: []
        }
      },
      {
        name: 'recommend_driver_positioning',
        description: 'Get AI-powered driver positioning recommendations',
        parameters: {
          type: 'object',
          properties: {
            current_drivers: { type: 'array' },
            time_horizon: { type: 'number', default: 60 },
            max_recommendations: { type: 'number', default: 10 }
          },
          required: ['current_drivers']
        }
      },
      {
        name: 'optimize_fleet_distribution',
        description: 'Optimize fleet distribution across locations',
        parameters: {
          type: 'object',
          properties: {
            total_drivers: { type: 'number' },
            target_areas: { type: 'array' },
            optimization_goal: { type: 'string', enum: ['efficiency', 'coverage', 'revenue'], default: 'efficiency' }
          },
          required: ['total_drivers']
        }
      },
      {
        name: 'predict_maintenance_needs',
        description: 'Predict vehicle maintenance needs using AI',
        parameters: {
          type: 'object',
          properties: {
            vehicle_ids: { type: 'array' },
            prediction_horizon_days: { type: 'number', default: 7 },
            urgency_threshold: { type: 'string', enum: ['low', 'medium', 'high'], default: 'medium' }
          },
          required: []
        }
      }
    ]
  });
});

// Error handling
app.use((err, req, res, next) => {
  logger.error('Unhandled error:', err);
  res.status(500).json({ error: 'Internal server error' });
});

const PORT = process.env.PORT || 8080;
server.listen(PORT, () => {
  logger.info(`MCP Server running on port ${PORT}`);
});

module.exports = app;
