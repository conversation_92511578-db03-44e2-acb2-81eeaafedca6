import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { MapPin, Clock, BikeIcon as MotorbikeIcon, Star, ArrowRight, User, Wallet, Bell } from 'lucide-react'

export default function DashboardPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex flex-col md:flex-row gap-8">
        <div className="md:w-1/4">
          <Card>
            <CardHeader>
              <div className="flex flex-col items-center">
                <Avatar className="h-24 w-24 mb-4">
                  <AvatarImage src="/placeholder-user.jpg" alt="User" />
                  <AvatarFallback>UN</AvatarFallback>
                </Avatar>
                <CardTitle><PERSON><PERSON><PERSON></CardTitle>
                <CardDescription>Member since March 2023</CardDescription>
              </div>
            </CardHeader>
            <CardContent>
              <div className="flex justify-center gap-4 mb-6">
                <div className="text-center">
                  <p className="text-2xl font-bold">42</p>
                  <p className="text-sm text-muted-foreground">Rides</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold">4.8</p>
                  <p className="text-sm text-muted-foreground">Rating</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold">₹2,460</p>
                  <p className="text-sm text-muted-foreground">Earned</p>
                </div>
              </div>

              <nav className="space-y-2">
                <Button variant="ghost" className="w-full justify-start" asChild>
                  <Link href="/dashboard">
                    <User className="mr-2 h-4 w-4" />
                    My Profile
                  </Link>
                </Button>
                <Button variant="ghost" className="w-full justify-start" asChild>
                  <Link href="/payments">
                    <Wallet className="mr-2 h-4 w-4" />
                    My Wallet
                  </Link>
                </Button>
                <Button variant="ghost" className="w-full justify-start" asChild>
                  <Link href="/dashboard/notifications">
                    <Bell className="mr-2 h-4 w-4" />
                    Notifications
                  </Link>
                </Button>
              </nav>
            </CardContent>
            <CardFooter>
              <Button variant="outline" className="w-full">
                Sign Out
              </Button>
            </CardFooter>
          </Card>
        </div>

        <div className="md:w-3/4">
          <h1 className="text-3xl font-bold mb-6">Dashboard</h1>

          <Tabs defaultValue="upcoming">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="upcoming">Upcoming Rides</TabsTrigger>
              <TabsTrigger value="history">Ride History</TabsTrigger>
              <TabsTrigger value="earnings">Earnings</TabsTrigger>
            </TabsList>

            <TabsContent value="upcoming" className="space-y-4 mt-6">
              <Card>
                <div className="flex flex-col md:flex-row">
                  <CardHeader className="md:w-1/4 flex-shrink-0 border-r border-border">
                    <div className="flex flex-col">
                      <Badge className="self-start mb-2">Today</Badge>
                      <h3 className="font-semibold">Passenger: Neha R.</h3>
                      <div className="flex items-center mt-1">
                        <Star className="h-4 w-4 text-yellow-500 fill-yellow-500 mr-1" />
                        <span>4.9</span>
                      </div>
                      <div className="mt-4">
                        <Badge variant="outline" className="flex items-center gap-1">
                          <MotorbikeIcon className="h-3 w-3" />
                          Your Vehicle
                        </Badge>
                      </div>
                    </div>
                  </CardHeader>

                  <CardContent className="md:w-2/4 flex-grow p-6">
                    <div className="flex items-center mb-4">
                      <Clock className="h-4 w-4 mr-2 text-muted-foreground" />
                      <span>08:30 AM</span>
                    </div>

                    <div className="relative pl-6 border-l border-dashed border-border">
                      <div className="absolute left-0 top-0 w-3 h-3 -ml-1.5 rounded-full bg-green-500"></div>
                      <div className="mb-6">
                        <p className="text-lg font-semibold">Andheri East</p>
                      </div>

                      <div className="absolute left-0 bottom-0 w-3 h-3 -ml-1.5 rounded-full bg-primary"></div>
                      <div>
                        <p className="text-lg font-semibold">Bandra Kurla Complex</p>
                      </div>
                    </div>

                    <div className="mt-4 flex items-center text-sm text-muted-foreground">
                      <MapPin className="h-4 w-4 mr-1" />
                      <span>12 km</span>
                      <Clock className="h-4 w-4 ml-4 mr-1" />
                      <span>~30 min</span>
                    </div>
                  </CardContent>

                  <CardFooter className="md:w-1/4 flex-shrink-0 border-l border-border p-6 flex flex-col items-center justify-center">
                    <div className="text-center mb-4">
                      <p className="text-sm text-muted-foreground">Earnings</p>
                      <p className="text-3xl font-bold">₹36</p>
                      <p className="text-xs text-muted-foreground">₹3 per km</p>
                    </div>

                    <Button className="w-full">
                      Start Ride
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  </CardFooter>
                </div>
              </Card>

              <Card>
                <div className="flex flex-col md:flex-row">
                  <CardHeader className="md:w-1/4 flex-shrink-0 border-r border-border">
                    <div className="flex flex-col">
                      <Badge className="self-start mb-2">Tomorrow</Badge>
                      <h3 className="font-semibold">Passenger: Raj K.</h3>
                      <div className="flex items-center mt-1">
                        <Star className="h-4 w-4 text-yellow-500 fill-yellow-500 mr-1" />
                        <span>4.7</span>
                      </div>
                      <div className="mt-4">
                        <Badge variant="outline" className="flex items-center gap-1">
                          <MotorbikeIcon className="h-3 w-3" />
                          Your Vehicle
                        </Badge>
                      </div>
                    </div>
                  </CardHeader>

                  <CardContent className="md:w-2/4 flex-grow p-6">
                    <div className="flex items-center mb-4">
                      <Clock className="h-4 w-4 mr-2 text-muted-foreground" />
                      <span>09:00 AM</span>
                    </div>

                    <div className="relative pl-6 border-l border-dashed border-border">
                      <div className="absolute left-0 top-0 w-3 h-3 -ml-1.5 rounded-full bg-green-500"></div>
                      <div className="mb-6">
                        <p className="text-lg font-semibold">Andheri East</p>
                      </div>

                      <div className="absolute left-0 bottom-0 w-3 h-3 -ml-1.5 rounded-full bg-primary"></div>
                      <div>
                        <p className="text-lg font-semibold">Bandra Kurla Complex</p>
                      </div>
                    </div>

                    <div className="mt-4 flex items-center text-sm text-muted-foreground">
                      <MapPin className="h-4 w-4 mr-1" />
                      <span>12 km</span>
                      <Clock className="h-4 w-4 ml-4 mr-1" />
                      <span>~30 min</span>
                    </div>
                  </CardContent>

                  <CardFooter className="md:w-1/4 flex-shrink-0 border-l border-border p-6 flex flex-col items-center justify-center">
                    <div className="text-center mb-4">
                      <p className="text-sm text-muted-foreground">Earnings</p>
                      <p className="text-3xl font-bold">₹36</p>
                      <p className="text-xs text-muted-foreground">₹3 per km</p>
                    </div>

                    <Button variant="outline" className="w-full">
                      Cancel Ride
                    </Button>
                  </CardFooter>
                </div>
              </Card>
            </TabsContent>

            <TabsContent value="history" className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle>Ride History</CardTitle>
                  <CardDescription>Your past rides and earnings</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {[...Array(5)].map((_, i) => (
                      <div key={i} className="flex justify-between items-center border-b pb-4">
                        <div>
                          <p className="font-medium">Andheri East → BKC</p>
                          <p className="text-sm text-muted-foreground">
                            {new Date(Date.now() - i * ********).toLocaleDateString()} • 12 km
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="font-medium">₹36</p>
                          <p className="text-sm text-muted-foreground">
                            Passenger: {["Neha R.", "Raj K.", "Amit S.", "Priya M.", "Vikram J."][i]}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
                <CardFooter>
                  <Button variant="outline" className="w-full">
                    View All History
                  </Button>
                </CardFooter>
              </Card>
            </TabsContent>

            <TabsContent value="earnings" className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle>Earnings Overview</CardTitle>
                  <CardDescription>Your earnings from ride sharing</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-3 gap-4 mb-6">
                    <Card>
                      <CardHeader className="pb-2">
                        <CardDescription>Today</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <p className="text-2xl font-bold">₹72</p>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardHeader className="pb-2">
                        <CardDescription>This Week</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <p className="text-2xl font-bold">₹360</p>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardHeader className="pb-2">
                        <CardDescription>This Month</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <p className="text-2xl font-bold">₹1,440</p>
                      </CardContent>
                    </Card>
                  </div>

                  <div className="space-y-4">
                    <h3 className="font-medium">Recent Earnings</h3>
                    {[...Array(5)].map((_, i) => (
                      <div key={i} className="flex justify-between items-center border-b pb-4">
                        <div>
                          <p className="font-medium">
                            Ride with {["Neha R.", "Raj K.", "Amit S.", "Priya M.", "Vikram J."][i]}
                          </p>
                          <p className="text-sm text-muted-foreground">
                            {new Date(Date.now() - i * ********).toLocaleDateString()} • 12 km
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="font-medium">₹36</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
                <CardFooter>
                  <Button className="w-full">Withdraw to Bank Account</Button>
                </CardFooter>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  )
}

