#!/bin/bash

# Two-Wheeler Sharing AI Automation Setup Script
# This script sets up the complete workflow automation system

set -e

echo "🚀 Setting up Two-Wheeler Sharing AI Automation System..."

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Create necessary directories
echo "📁 Creating directory structure..."
mkdir -p logs
mkdir -p nginx/ssl
mkdir -p n8n/workflows
mkdir -p data/mongodb
mkdir -p data/postgres
mkdir -p data/redis

# Set permissions
chmod +x setup.sh
chmod 755 nginx/
chmod 755 n8n/workflows/

# Create environment file
echo "🔧 Creating environment configuration..."
cat > .env << EOF
# Application Configuration
NODE_ENV=development
PORT=3000
FRONTEND_URL=http://localhost:3000

# Database Configuration
MONGODB_URI=***************************************************************************
POSTGRES_URL=*************************************/n8n
REDIS_URL=redis://redis:6379

# n8n Configuration
N8N_BASIC_AUTH_ACTIVE=true
N8N_BASIC_AUTH_USER=admin
N8N_BASIC_AUTH_PASSWORD=admin123
N8N_WEBHOOK_URL=http://localhost:5678/webhook

# MCP Server Configuration
MCP_SERVER_URL=http://localhost:8080
OLLAMA_URL=http://localhost:11434

# External API Keys (Replace with actual keys)
OPENWEATHERMAP_API_KEY=sample_key_12345
GOOGLE_MAPS_API_KEY=sample_key_12345
FIREBASE_API_KEY=sample_key_12345

# Security
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
ENCRYPTION_KEY=your-32-character-encryption-key

# Notification Services
TWILIO_ACCOUNT_SID=your_twilio_sid
TWILIO_AUTH_TOKEN=your_twilio_token
SENDGRID_API_KEY=your_sendgrid_key
EOF

echo "✅ Environment file created. Please update API keys in .env file."

# Pull and start services
echo "🐳 Starting Docker services..."
docker-compose pull
docker-compose up -d

# Wait for services to be ready
echo "⏳ Waiting for services to start..."
sleep 30

# Check service health
echo "🔍 Checking service health..."

# Check MongoDB
echo "Checking MongoDB..."
docker-compose exec -T mongodb mongosh --eval "db.adminCommand('ping')" || echo "⚠️  MongoDB not ready yet"

# Check PostgreSQL
echo "Checking PostgreSQL..."
docker-compose exec -T postgres pg_isready -U n8n || echo "⚠️  PostgreSQL not ready yet"

# Check Redis
echo "Checking Redis..."
docker-compose exec -T redis redis-cli ping || echo "⚠️  Redis not ready yet"

# Setup Ollama models
echo "🤖 Setting up Ollama AI models..."
docker-compose exec -T ollama ollama pull llama2 || echo "⚠️  Ollama model download failed - will retry later"

# Import n8n workflows
echo "📋 Importing n8n workflows..."
sleep 10

# Create initial database collections and indexes
echo "🗄️  Setting up database..."
cat > init-db.js << EOF
// Initialize MongoDB collections and indexes
use('two-wheeler-sharing');

// Create collections
db.createCollection('users');
db.createCollection('drivers');
db.createCollection('rides');
db.createCollection('rewards');
db.createCollection('notifications');

// Create indexes for better performance
db.users.createIndex({ "email": 1 }, { unique: true });
db.drivers.createIndex({ "email": 1 }, { unique: true });
db.drivers.createIndex({ "location": "2dsphere" });
db.rides.createIndex({ "userId": 1 });
db.rides.createIndex({ "driverId": 1 });
db.rides.createIndex({ "status": 1 });
db.rides.createIndex({ "createdAt": -1 });
db.rewards.createIndex({ "userId": 1 });
db.rewards.createIndex({ "createdAt": -1 });
db.notifications.createIndex({ "userId": 1 });
db.notifications.createIndex({ "read": 1 });

print("Database initialized successfully!");
EOF

docker-compose exec -T mongodb mongosh two-wheeler-sharing < init-db.js
rm init-db.js

# Install application dependencies
echo "📦 Installing application dependencies..."
if [ -f "package.json" ]; then
    npm install
else
    echo "⚠️  package.json not found. Please run 'npm install' manually."
fi

# Install MCP server dependencies
echo "📦 Installing MCP server dependencies..."
cd mcp-server
npm install
cd ..

echo "🎉 Setup completed successfully!"
echo ""
echo "🌐 Access URLs:"
echo "  • Main Application: http://localhost:3000"
echo "  • n8n Workflows: http://localhost:5678 (admin/admin123)"
echo "  • MCP Server: http://localhost:8080"
echo "  • Admin Dashboard: http://localhost:3000/admin"
echo "  • Driver Dashboard: http://localhost:3000/driver"
echo ""
echo "📚 Next Steps:"
echo "  1. Update API keys in .env file"
echo "  2. Access n8n at http://localhost:5678 to configure workflows"
echo "  3. Test the ride matching workflow"
echo "  4. Monitor system health in admin dashboard"
echo ""
echo "🔧 Useful Commands:"
echo "  • View logs: docker-compose logs -f [service-name]"
echo "  • Restart services: docker-compose restart"
echo "  • Stop all services: docker-compose down"
echo "  • Update services: docker-compose pull && docker-compose up -d"
echo ""
echo "📖 For detailed documentation, see README.md"
EOF
